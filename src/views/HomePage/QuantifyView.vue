<template>
    <div class="quantify-view">
        <!-- 顶部：左侧概览 + 右侧主网地图 -->
        <div class="top-row">
            <!-- 左侧概览 -->
            <div class="left-panel">
                <div class="panel">
                    <div class="panel-title">供电情况概览</div>
                    <div class="overview-layout">
                        <!-- 左侧：上下结构（上：两个环形图；下：雷达图） -->
                        <div class="overview-left">
                            <div class="donut-row">
                                <div class="chart donut" ref="pie1Ref"></div>
                                <div class="chart donut" ref="pie2Ref"></div>
                            </div>
                            <div class="donut-legend">
                                <div class="legend-item" v-for="item in pieData" :key="item.name">
                                    <span class="legend-dot" :style="{ backgroundColor: item.color }"></span>
                                    <span class="legend-text">{{ item.name }}</span>
                                </div>
                            </div>
                            <div class="radar-block">
                                <div class="chart radar" ref="radarRef"></div>
                            </div>
                        </div>
                        <!-- 右侧：四个指标卡片，等高分布 -->
                        <div class="metric-cards right">
                            <div class="metric-card" v-for="m in metrics" :key="m.title">
                                <div class="metric-icon">▶</div>
                                <div>
                                    <div class="metric-title">{{ m.title }}</div>
                                    <div class="metric-value">{{ m.value }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧主网地图 -->
            <div class="map-panel panel">
                <div class="panel-title">主网地图</div>
                <div class="map-wrap">
                    <div ref="mapRef" class="map"></div>
                    <div class="legend">
                        <div class="legend-title">供电负荷</div>
                        <div class="legend-item"><span class="dot" style="background:#00ff7a"></span>100%及以上</div>
                        <div class="legend-item"><span class="dot" style="background:#38ffb0"></span>80-100%</div>
                        <div class="legend-item"><span class="dot" style="background:#b0ffd9"></span>50-80%</div>
                        <div class="legend-item"><span class="dot" style="background:#ffe680"></span>30-50%</div>
                        <div class="legend-item"><span class="dot" style="background:#ffad5c"></span>0-30%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部两块：左侧折线图占位 + 右侧表格占位（后续接入真实数据） -->
        <div class="bottom-row">
            <div class="panel left-bottom">
                <div class="panel-title">电力平衡分析</div>
                <div class="chart line" ref="lineRef"></div>
            </div>
            <div class="panel right-bottom">
                <div class="panel-title">电力平衡分析</div>
                <div class="table-wrap">
                    <div class="subtable">
                        <div class="subtable-title">分区调节能力</div>
                        <div class="subtable-header">
                            <div>分区名称</div>
                            <div>最大上调节能力</div>
                            <div>最大下调节能力</div>
                        </div>
                        <div class="subtable-row" v-for="r in abilityRows" :key="r.name">
                            <div>{{ r.name }}</div>
                            <div>{{ r.up }}</div>
                            <div>{{ r.down }}</div>
                        </div>
                    </div>
                    <div class="subtable">
                        <div class="subtable-title">分区供电能力分析</div>
                        <div class="subtable-header">
                            <div>分区名称</div>
                            <div>最低/平均裕度</div>
                            <div>缺电/紧张时长</div>
                        </div>
                        <div class="subtable-row" v-for="r in supplyRows" :key="r.name">
                            <div>{{ r.name }}</div>
                            <div>{{ r.margin }}</div>
                            <div>{{ r.shortage }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { registerMapCopy } from '@/utils/common.js'
import { inject, markRaw, onBeforeUnmount, onMounted, ref } from 'vue'

// 复用 App.vue provide 的 echarts 实例
const echarts = inject('ec')

const mapRef = ref(null)
const mapChart = ref()
const mapPartition = '全省' // 直接复用 public/jsons/全省.geojson

const pie1Ref = ref(null)
const pie2Ref = ref(null)
const radarRef = ref(null)
const lineRef = ref(null)

const charts = {
    pie1: null,
    pie2: null,
    radar: null,
    line: null,
}
let lineRO = null
let mapRO = null

const pieData = [
    { name: '火电', color: '#FFD700' },
    { name: '风电', color: '#52D273' },
    { name: '光伏', color: '#86FB8B' },
    { name: '储能', color: '#1FAA59' },
]

const metrics = [
    { title: '最高调度负荷', value: 'XXX' },
    { title: '最大供电缺口', value: 'XXX' },
    { title: '最大日峰谷差', value: 'XXX' },
    { title: '最大调峰缺口', value: 'XXX' },
]

const abilityRows = [
    { name: '分区1', up: 'XXXX', down: 'XXXX' },
    { name: '分区2', up: 'XXXX', down: 'XXXX' },
    { name: '分区3', up: 'XXXX', down: 'XXXX' },
    { name: '分区4', up: 'XXXX', down: 'XXXX' },
    { name: '分区5', up: 'XXXX', down: 'XXXX' },
]
const supplyRows = [
    { name: '分区1', margin: 'XXXX', shortage: 'XXXX' },
    { name: '分区2', margin: 'XXXX', shortage: 'XXXX' },
    { name: '分区3', margin: 'XXXX', shortage: 'XXXX' },
    { name: '分区4', margin: 'XXXX', shortage: 'XXXX' },
    { name: '分区5', margin: 'XXXX', shortage: 'XXXX' },
]

onMounted(async () => {
    await registerMapCopy(mapPartition)
    mapChart.value = markRaw(echarts.init(mapRef.value))
    const option = buildMapOption()
    mapChart.value.setOption(option)
    initCharts()
    // 监听容器尺寸变化，防止图表溢出
    if (window.ResizeObserver) {
        lineRO = new ResizeObserver(() => { charts.line && charts.line.resize() })
        lineRO.observe(lineRef.value)
        mapRO = new ResizeObserver(() => { mapChart.value && mapChart.value.resize() })
        mapRO.observe(mapRef.value)
    }
    window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    if (mapChart.value) {
        mapChart.value.dispose()
        mapChart.value = undefined
    }
    Object.keys(charts).forEach(k => {
        if (charts[k]) charts[k].dispose()
        charts[k] = null
    })
    if (lineRO) { lineRO.disconnect(); lineRO = null }
    if (mapRO) { mapRO.disconnect(); mapRO = null }
})

const handleResize = () => {
    mapChart.value && mapChart.value.resize()
    charts.pie1 && charts.pie1.resize()
    charts.pie2 && charts.pie2.resize()
    charts.radar && charts.radar.resize()
    charts.line && charts.line.resize()
}

const buildMapOption = () => {
    const baseItemStyle = {
        areaColor: '#0b2322',
        borderColor: '#86FB8B',
        borderWidth: 0.8,
        shadowColor: 'rgba(134,251,139,0.2)',
        shadowBlur: 6
    }
    return {
        backgroundColor: 'transparent',
        tooltip: { show: false },
        geo: {
            map: mapPartition,
            roam: true,
            zoom: 1.1,
            label: { show: false, color: '#c7ffef' },
            itemStyle: baseItemStyle,
            emphasis: {
                itemStyle: { areaColor: '#0f3a30' }
            }
        },
        series: [
            {
                type: 'map',
                map: mapPartition,
                roam: true,
                zoom: 1.1,
                label: { show: false },
                itemStyle: baseItemStyle,
                emphasis: { itemStyle: { areaColor: '#0f3a30' } }
            },
            {
                name: '站点',
                type: 'effectScatter',
                coordinateSystem: 'geo',
                data: getMockStations(),
                symbolSize: 6,
                rippleEffect: { brushType: 'stroke' },
                itemStyle: { color: '#86FB8B' }
            }
        ]
    }
}

function initCharts() {
    charts.pie1 = markRaw(echarts.init(pie1Ref.value))
    charts.pie1.setOption(buildDonutOption('装机容量占比'))
    charts.pie2 = markRaw(echarts.init(pie2Ref.value))
    charts.pie2.setOption(buildDonutOption('发电量占比'))
    charts.radar = markRaw(echarts.init(radarRef.value))
    charts.radar.setOption(buildRadarOption())
    charts.line = markRaw(echarts.init(lineRef.value))
    charts.line.setOption(buildLineOption())
}

function buildDonutOption(title) {
    return {
        title: { text: title, left: 'center', top: 6, textStyle: { color: '#d4ffdf', fontSize: 12 } },
        tooltip: { show: false },
        legend: { show: false },
        series: [
            {
                type: 'pie',
                radius: ['62%', '82%'],
                center: ['50%', '55%'],
                avoidLabelOverlap: false,
                label: { show: false },
                data: [
                    { value: 35, name: '火电', itemStyle: { color: '#FFD700' } },
                    { value: 25, name: '风电', itemStyle: { color: '#52D273' } },
                    { value: 22, name: '光伏', itemStyle: { color: '#86FB8B' } },
                    { value: 18, name: '储能', itemStyle: { color: '#1FAA59' } },
                ]
            }
        ]
    }
}

function buildRadarOption() {
    return {
        tooltip: { show: false },
        radar: {
            radius: '68%',
            splitNumber: 4,
            axisName: { color: '#d4ffdf', fontSize: 10 },
            splitLine: { lineStyle: { color: 'rgba(134,251,139,0.25)' } },
            splitArea: { areaStyle: { color: ['rgba(134,251,139,0.05)', 'rgba(134,251,139,0.02)'] } },
            indicator: [
                { name: '正常用容量', max: 100 },
                { name: '负荷用容量', max: 100 },
                { name: '无缝电源', max: 100 },
                { name: '系统稳定', max: 100 },
                { name: '外来电保持', max: 100 },
            ]
        },
        series: [{
            type: 'radar',
            symbol: 'none',
            areaStyle: { color: 'rgba(134,251,139,0.35)' },
            lineStyle: { color: '#86FB8B' },
            data: [{ value: [70, 65, 55, 60, 68] }]
        }]
    }
}

function buildLineOption() {
    const axisStyle = { lineStyle: { color: 'rgba(134,251,139,0.3)' }, axisLabel: { color: '#d4ffdf' }, splitLine: { show: true, lineStyle: { color: 'rgba(134,251,139,0.08)' } } }
    return {
        tooltip: { trigger: 'axis' },
        grid: { left: 30, right: 20, top: 20, bottom: 60 },
        xAxis: { type: 'category', data: Array.from({ length: 24 }, (_, i) => `${i}:00`), ...axisStyle },
        yAxis: { type: 'value', ...axisStyle },
        legend: { data: ['用电负荷', '最小可调出力', '最大功力资源'], textStyle: { color: '#d4ffdf' } },
        dataZoom: [
            {
                type: 'slider',
                show: true,
                bottom: 10,
                start: 0,
                end: 100,
                height: 20,
                handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#86FB8B',
                    shadowBlur: 3,
                    shadowColor: 'rgba(134,251,139,0.6)',
                    shadowOffsetX: 2,
                    shadowOffsetY: 2
                },
                textStyle: {
                    color: '#d4ffdf'
                },
                borderColor: 'rgba(134,251,139,0.5)',
                fillerColor: 'rgba(134,251,139,0.2)',
                backgroundColor: 'rgba(6,23,17,0.6)'
            }
        ],
        series: [
            { name: '用电负荷', type: 'line', smooth: true, areaStyle: { color: 'rgba(134,251,139,0.20)' }, lineStyle: { color: '#52D273' }, data: mockSeries(24, 180, 240) },
            { name: '最小可调出力', type: 'line', smooth: true, areaStyle: { color: 'rgba(31,170,89,0.20)' }, lineStyle: { color: '#1FAA59' }, data: mockSeries(24, 120, 180) },
            { name: '最大功力资源', type: 'line', smooth: true, areaStyle: { color: 'rgba(104,211,145,0.18)' }, lineStyle: { color: '#68D391' }, data: mockSeries(24, 220, 300) },
        ]
    }
}

function mockSeries(n, min, max) {
    const arr = []
    for (let i = 0; i < n; i++) {
        arr.push(Math.round(min + Math.random() * (max - min)))
    }
    return arr
}

function getMockStations() {
    const pts = [
        { name: '郑州', value: [113.65, 34.76] },
        { name: '洛阳', value: [112.44, 34.70] },
        { name: '开封', value: [114.30, 34.80] },
        { name: '新乡', value: [113.90, 35.30] },
        { name: '许昌', value: [113.85, 34.02] },
        { name: '南阳', value: [112.53, 33.00] },
        { name: '商丘', value: [115.65, 34.44] },
        { name: '焦作', value: [113.25, 35.22] },
        { name: '平顶山', value: [113.30, 33.75] },
        { name: '周口', value: [114.65, 33.62] },
    ]
    return pts
}
</script>

<style scoped lang="scss">
.quantify-view {
    padding: 20px;
    min-height: calc(100vh - 140px);
}

.top-row {
    display: flex;
    gap: 20px;
    align-items: stretch;
}

.left-panel {
    width: 42%;
}

.map-panel {
    width: 58%;
}

/* 让左右两块等高并让内部充满 */
.left-panel,
.map-panel {
    display: flex;
}

.left-panel>.panel {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.map-panel.panel {
    display: flex;
    flex-direction: column;
}

.panel {
    background: #0A1A12;
    border: 1px solid rgba(134, 251, 139, .85);
    border-radius: 8px;
    padding: 16px;
    color: #d1ffe6;
}

.panel-title {
    height: 36px;
    line-height: 36px;
    text-align: left;
    color: #86FB8B;
    font-weight: 700;
    padding: 0 8px 8px 2px;
}

.overview-layout {
    display: grid;
    grid-template-columns: 1fr 260px;
    gap: 16px;
    align-items: stretch;
    height: 100%;
}

.overview-left {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 14px;
}

.donut-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
}



.donut-legend {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 12px;
    padding: 0 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

.legend-text {
    color: #d4ffdf;
    font-size: 12px;
    line-height: 1;
}

.radar-block {
    background: rgba(6, 23, 17, 0.6);
    border: 1px dashed rgba(134, 251, 139, 0.22);
    border-radius: 6px;
    padding: 8px;
}

.chart {
    background: rgba(6, 23, 17, 0.6);
    border: 1px dashed rgba(134, 251, 139, 0.22);
    border-radius: 6px;
}

.chart.donut {
    height: 180px;
}

.chart.radar {
    height: 220px;
}

.chart.box-placeholder {
    height: 220px;
}

.metric-cards {
    display: grid;
    gap: 14px;
}

.metric-cards.right {
    height: 100%;
    grid-template-rows: repeat(4, 1fr);
}

.metric-card {
    display: grid;
    grid-template-columns: 36px 1fr;
    align-items: center;
    padding: 12px 14px;
    background: linear-gradient(180deg, rgba(134, 251, 139, 0.10) 0%, rgba(6, 23, 17, 0.55) 100%);
    border: 1px solid rgba(134, 251, 139, .85);
    border-radius: 8px;
}

.metric-icon {
    color: #86FB8B;
    font-weight: 900;
    font-size: 16px;
}

.metric-title {
    color: #d4ffdf;
    font-size: 14px;
}

.metric-value {
    color: #ffffff;
    font-size: 16px;
    font-weight: 700;
    margin-top: 4px;
}

.map-wrap {
    position: relative;
    flex: 1;
    display: flex;
}

.map {
    height: 100%;
    width: 100%;
}

.legend {
    position: absolute;
    right: 12px;
    top: 16px;
    width: 160px;
    background: rgba(0, 0, 0, 0.35);
    border: 1px solid rgba(134, 251, 139, .85);
    border-radius: 8px;
    padding: 10px 12px;
}

.legend-title {
    color: #c7ffef;
    font-weight: 700;
    margin-bottom: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #c7ffef;
    font-size: 12px;
    margin: 6px 0;
}

.legend-item .dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.bottom-row {
    margin-top: 20px;
    display: grid;
    grid-template-columns: 42% 58%;
    gap: 20px;
}

.left-bottom {
    position: relative;
    overflow: hidden;
}

.left-bottom .line {
    height: 260px;
    width: 100%;
}

.table-wrap {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.subtable {
    border: 1px solid rgba(134, 251, 139, .85);
    border-radius: 6px;
    padding: 8px;
    background: rgba(6, 23, 17, 0.6);
}

.subtable-title {
    color: #86FB8B;
    font-weight: 700;
    margin: 6px 4px 10px;
}

.subtable-header,
.subtable-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
}

.subtable-header {
    color: #d4ffdf;
    background: rgba(134, 251, 139, 0.10);
    padding: 8px;
    border-radius: 4px;
}

.subtable-row {
    color: #d4ffdf;
    padding: 8px;
    border-bottom: 1px dashed rgba(134, 251, 139, 0.20);
}

.subtable-row:last-child {
    border-bottom: none;
}
</style>
